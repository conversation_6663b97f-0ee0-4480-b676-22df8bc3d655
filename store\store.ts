import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./slices/authSlice";
import vehicleReducer from "./slices/vehicleSlice";
import brandReducer from "./slices/brandSlice";
import adReducer from "./slices/adSlice";
import vehicleDetailReducer from "./slices/vehicleDetailSlice";
import sellVehicleReducer from "./slices/sellVehicleSlice";
import accountReducer from "./slices/accountSlice";
import packagesReducer from "./slices/packagesSlice";
import manageVehiclesReducer from "./slices/manageVehiclesSlice";
import filterReducer from "./slices/filterSlice";
import editVehicleReducer from "./slices/editVehicleSlice";
import contactReducer from "./slices/contactSlice";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    vehicles: vehicleReducer,
    brands: brandReducer,
    ads: adReducer,
    vehicleDetail: vehicleDetailReducer,
    sellVehicle: sellVehicleReducer,
    account: accountReducer,
    packages: packagesReducer,
    manageVehicles: manageVehiclesReducer,
    filter: filterReducer,
    editVehicle: editVehicleReducer,
    contact: contactReducer,
  },
  middleware: (getDefaultMiddleware) => getDefaultMiddleware(),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
