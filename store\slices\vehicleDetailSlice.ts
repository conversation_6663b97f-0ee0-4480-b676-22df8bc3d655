import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { VehicleType } from "./vehicleSlice";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

interface VehicleDetail {
  id: number;
  user_id: number;
  title: string;
  brand: string;
  model: string;
  variant: string;
  year: string;
  fuel_type: string;
  kilometers_driven: number;
  price: string;
  make_offer: string | null;
  transmission: string;
  ownership: string;
  location: string;
  description: string;
  images: string[];
  status: string;
  is_verified: boolean;
  primary_image: string;
  insurance_type?: string;
  user: {
    phone: string;
  };
}

interface VehicleDetailState {
  vehicle: VehicleDetail | null;
  loading: boolean;
  error: string | null;
}

const initialState: VehicleDetailState = {
  vehicle: null,
  loading: false,
  error: null,
};

export const fetchVehicleDetail = createAsyncThunk<
  VehicleDetail,
  { id: number; type: VehicleType },
  { rejectValue: string }
>(
  "vehicleDetail/fetchVehicleDetail",
  async ({ id, type }, { rejectWithValue }) => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/display/vehicals/detail?id=${id}&type=${type}`
      );

      // Check if response is ok
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Check content type
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        throw new Error("Invalid response format");
      }

      const data = await response.json();

      if (!data.status) {
        throw new Error(data.message || "Failed to fetch vehicle details");
      }

      return data.data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : "Failed to fetch vehicle details"
      );
    }
  }
);

const vehicleDetailSlice = createSlice({
  name: "vehicleDetail",
  initialState,
  reducers: {
    clearVehicleDetail: (state) => {
      state.vehicle = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchVehicleDetail.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVehicleDetail.fulfilled, (state, action) => {
        state.loading = false;
        state.vehicle = action.payload;
      })
      .addCase(fetchVehicleDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch vehicle details";
      });
  },
});

export const { clearVehicleDetail } = vehicleDetailSlice.actions;
export default vehicleDetailSlice.reducer;
